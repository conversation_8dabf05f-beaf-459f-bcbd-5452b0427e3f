import { useEffect } from 'react';
import { LoadingOverlay, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { VendorConnectModal } from './components/VendorConnectModal/VendorConnectModal';
import { VendorItem } from './components/VendorItem/VendorItem';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { VendorType } from '@/types';
import { getPriority } from './utils/getPriority';

export const Vendors = () => {
  const { vendors, getVendors: getVendorsRequest } = useVendorsStore();
  const { account } = useAccountStore();

  const { apiRequest: getVendors, isLoading } = useAsyncRequest({
    apiFunc: getVendorsRequest,
  });

  useEffect(() => {
    getVendors();
  }, [getVendors]);

  const preferredVendorOrderMapState = (
    account?.gpo?.preferredVendors ?? []
  ).reduce<Record<string, number>>(
    (acc, { vendorId, order }) => ({
      ...acc,
      [vendorId]: order,
    }),
    {},
  );

  const sortedVendors = [...vendors].sort(
    (vendor1: VendorType, vendor2: VendorType) => {
      const preferredVendor1Order = preferredVendorOrderMapState[vendor1.id];
      const preferredVendor2Order = preferredVendorOrderMapState[vendor2.id];

      const vendor1IsPreferred = preferredVendor1Order !== undefined;
      const vendor1IsConnected = vendor1.status === 'connected';

      const vendor1Priority = getPriority(
        vendor1IsPreferred,
        vendor1IsConnected,
      );

      const vendor2IsPreferred = preferredVendor2Order !== undefined;
      const vendor2IsConnected = vendor2.status === 'connected';

      const vendor2Priority = getPriority(
        vendor2IsPreferred,
        vendor2IsConnected,
      );

      if (vendor1Priority !== vendor2Priority) {
        return vendor1Priority - vendor2Priority;
      }

      if (vendor1IsPreferred && vendor2IsPreferred) {
        return preferredVendor1Order - preferredVendor2Order;
      }

      return vendor1.name.localeCompare(vendor2.name);
    },
  );

  return (
    <div className="mainSection">
      <PageHeader
        title="Vendor Management"
        description="Connect, manage and check connection status"
      />

      {sortedVendors.length ? (
        <Flex direction="column">
          {sortedVendors.map((vendor) => (
            <div key={vendor.id}>
              <VendorItem
                vendor={vendor}
                isPreferred={
                  preferredVendorOrderMapState[vendor.id] !== undefined
                }
              />
            </div>
          ))}
        </Flex>
      ) : (
        <Flex align="center" justify="center" mih="300px">
          <Text size="lg">No vendors found</Text>
        </Flex>
      )}

      <LoadingOverlay visible={isLoading} />
      <VendorConnectModal />
    </div>
  );
};
