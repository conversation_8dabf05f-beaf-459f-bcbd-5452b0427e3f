import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { Image, Text } from '@mantine/core';
import { Modal } from '@/components';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { AmazonVendorConnectModalForm } from './AmazonVendorConnectModalForm';
import { CommonVendorConnectModalForm } from './CommonVendorConnectModalForm';
import styles from './VendorConnectModal.module.css';
import { MODAL_NAME } from '@/constants';
import { CLINIC_UNIFIED_VENDOR_FORM_LINK } from './constants';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Button } from '@/libs/ui/Button/Button';
import { Progress } from '@radix-ui/react-progress';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export const VendorConnectModal = () => {
  const { modalOption } = useModalStore();
  const { vendor } = modalOption as VendorConnectModalOptions;

  if (!vendor) {
    return null;
  }

  const isReconnect = Boolean(vendor.lastProductCatalogSync);

  return (
    <Modal
      name={MODAL_NAME.VENDOR_CONNECT}
      size="md"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className="relative top-[-1.5rem]">
        <div className="flex w-full flex-col items-center">
          <div className="mb-4 rounded border border-black/[0.09] bg-white p-3">
            <Logo type="emblem" className="w-14" />
          </div>
          <p className="mb-2 text-xl font-semibold">Connect with your vendor</p>
          <p className="text-center text-sm text-black/70">
            Highfive & Amazon Business have a special offer of{' '}
            <span className="text-black">50% Amazon Business Prime</span> AND
            special discounted product pricing! Follow the steps below to redeem
            and connect.
          </p>
          <div className="mt-4 mb-8 flex w-96 flex-col items-center rounded-lg border border-black/[0.04] bg-[#F2F8FC] p-4 pb-10">
            <div className="mb-10 flex w-full justify-between">
              <span className="text-sm font-medium">Connect your Account</span>
              <span className="text-xs text-black/70">STEP 3/3</span>
              <Progress.Root>
                <Progress.Indicator />
              </Progress.Root>
            </div>
            <Button
              variant="white"
              className="mb-4 font-semibold"
              href="https://www.amazon.com/b2b/appcenter/applicationdetails/amzn1.sp.solution.b83d6cb9-da87-4673-ab22-f4854a79d9a4"
            >
              Log Into Your Amazon Business Account
            </Button>
            <Button variant="unstyled">
              <span className="underline">Previous Step</span>
            </Button>
          </div>
          <span className="text-[16px]">
            Need help?{' '}
            <Button variant="unstyled">
              <span className="underline">Watch our Tutorial</span>
            </Button>
          </span>
        </div>
      </div>
    </Modal>
  );
};
