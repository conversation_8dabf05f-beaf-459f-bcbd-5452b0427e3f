import * as RadixProgress from '@radix-ui/react-progress';
import { mergeClasses } from '@/utils/tailwind';

interface ProgressProps {
  percentage: number;
  color?: string;
  height?: string;
  className?: string;
}

export const Progress = ({
  percentage,
  color = '#518EF8',
  height = 'h-2',
  className,
}: ProgressProps) => {
  return (
    <RadixProgress.Root
      className={mergeClasses(
        'relative overflow-hidden bg-gray-200 rounded-full w-full',
        height,
        className,
      )}
      value={percentage}
    >
      <RadixProgress.Indicator
        className="h-full rounded-full transition-transform duration-500 ease-out"
        style={{
          backgroundColor: color,
          transform: `translateX(-${100 - percentage}%)`,
        }}
      />
    </RadixProgress.Root>
  );
};
